/**
 * <PERSON><PERSON> script to test the Telegram integration's message splitting functionality
 * This script demonstrates how the TelegramIntegration class handles long messages
 */

import { TelegramIntegration } from "../mastra/integrations/telegram";

// Mock implementation for demonstration
class MockTelegramIntegration extends TelegramIntegration {
  // Make private methods accessible for testing
  public async testSplitMessage(content: string): Promise<string[]> {
    return (this as any).fallbackSplitMessage(content);
  }

  public testEscapeMarkdown(text: string): string {
    return (this as any).escapeMarkdown(text);
  }

  public testFormatToolResult(result: any): string {
    return (this as any).formatToolResult(result);
  }
}

async function demonstrateSplitting() {
  console.log("🤖 Telegram Integration Demo\n");

  // Create a mock instance (won't actually connect to Telegram)
  const mockIntegration = new MockTelegramIntegration("demo-token");

  // Test 1: Short message (should not be split)
  console.log("📝 Test 1: Short Message");
  const shortMessage = "This is a short message that should not be split.";
  const shortParts = await mockIntegration.testSplitMessage(shortMessage);
  console.log(`Input length: ${shortMessage.length}`);
  console.log(`Parts: ${shortParts.length}`);
  console.log(`Content: "${shortParts[0]}"\n`);

  // Test 2: Long message (should be split)
  console.log("📝 Test 2: Long Message");
  const longMessage = "A".repeat(5000) + "\n\nThis is the end of a very long message.";
  const longParts = await mockIntegration.testSplitMessage(longMessage);
  console.log(`Input length: ${longMessage.length}`);
  console.log(`Parts: ${longParts.length}`);
  longParts.forEach((part, index) => {
    console.log(`Part ${index + 1} length: ${part.length}`);
  });
  console.log();

  // Test 3: Markdown escaping
  console.log("📝 Test 3: Markdown Escaping");
  const markdownText = "This has *bold*, _italic_, [links](url), and `code` formatting.";
  const escaped = mockIntegration.testEscapeMarkdown(markdownText);
  console.log(`Original: ${markdownText}`);
  console.log(`Escaped:  ${escaped}\n`);

  // Test 4: Tool result formatting
  console.log("📝 Test 4: Tool Result Formatting");
  const toolResult = {
    status: "success",
    data: {
      temperature: 75,
      humidity: 60,
      conditions: "sunny"
    },
    timestamp: new Date().toISOString()
  };
  const formatted = mockIntegration.testFormatToolResult(toolResult);
  console.log("Formatted tool result:");
  console.log(formatted);
}

// Run the demo
if (import.meta.url === `file://${process.argv[1]}`) {
  demonstrateSplitting().catch(console.error);
}

export { demonstrateSplitting };
