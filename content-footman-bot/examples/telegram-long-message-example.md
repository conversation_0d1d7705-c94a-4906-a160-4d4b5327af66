# Telegram Long Message Handling Example

This example demonstrates how the Telegram integration handles long messages that exceed the 1000 character threshold.

## How it works

1. **Initial Response**: When a user sends a message, the bot starts with "Thinking..."

2. **Short Messages (< 1000 chars)**: The bot updates the message in real-time as the AI generates the response.

3. **Long Messages (> 1000 chars)**: 
   - The bot stops updating the current message and keeps showing "Thinking..."
   - After the AI finishes generating the complete response, the "Thinking..." message is deleted
   - The complete response is split into smaller messages using an LLM
   - Each split message is sent separately

## Example Flow

### Short Message Response
```
User: "What's the weather like?"
Bot: "Thinking..." → "The weather is sunny and 75°F today."
```

### Long Message Response
```
User: "Explain quantum computing in detail"
Bot: "Thinking..." (stays like this during generation)
     ↓ (after completion, "Thinking..." is deleted)
Bot: "Quantum computing is a revolutionary technology..." (Message 1/3)
Bot: "The key principles include superposition..." (Message 2/3)  
Bot: "Applications include cryptography and..." (Message 3/3)
```

## Configuration

- `THINKING_THRESHOLD`: 1000 characters (when to stop live updates)
- `MAX_MESSAGE_LENGTH`: 4096 characters (Telegram's limit)
- `UPDATE_INTERVAL`: 500ms (how often to update during streaming)

## Benefits

1. **Better UX**: Users see immediate feedback with "Thinking..."
2. **No Truncation**: Long responses are fully delivered in multiple messages
3. **Intelligent Splitting**: LLM ensures message parts make sense independently
4. **Fallback Protection**: If LLM splitting fails, uses character-based splitting
