/**
 * Example usage of the searchAndGetProductDetailsTool
 * 
 * This tool combines Amazon product search with detailed product information fetching
 * in a single operation, providing comprehensive product data efficiently.
 */

import { searchAndGetProductDetailsTool } from '../src/mastra/tools/index.js';

async function exampleUsage() {
  try {
    // Example 1: Basic search with details
    console.log('Example 1: Basic search for wireless headphones');
    const result1 = await searchAndGetProductDetailsTool.execute({
      context: {
        topic: 'wireless headphones',
        limit: 5
      }
    });
    
    console.log(`Found ${result1.totalFound} products`);
    console.log(`Successfully fetched details for ${result1.productsWithDetails.filter(p => p.detailsSuccess).length} products`);
    
    // Example 2: Search with price filtering
    console.log('\nExample 2: Search with price range filtering');
    const result2 = await searchAndGetProductDetailsTool.execute({
      context: {
        topic: 'bluetooth speakers',
        limit: 3,
        lowPrice: 100,
        highPrice: 300
      }
    });
    
    console.log(`Found ${result2.totalFound} speakers in $100-$300 range`);
    
    // Example 3: Processing the detailed results
    console.log('\nExample 3: Processing detailed product information');
    result1.productsWithDetails.forEach((product, index) => {
      if (product.detailsSuccess && product.details) {
        console.log(`\nProduct ${index + 1}:`);
        console.log(`  Title: ${product.details.title}`);
        console.log(`  Price: ${product.details.price.formatted}`);
        console.log(`  Rating: ${product.details.rating.formatted}`);
        console.log(`  Features: ${product.details.features.length} items`);
        console.log(`  URL: ${product.details.url}`);
      }
    });
    
  } catch (error) {
    console.error('Error:', error);
  }
}

// Uncomment to run the example
// exampleUsage();

export { exampleUsage };
