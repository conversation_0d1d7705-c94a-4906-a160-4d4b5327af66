# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Repository Structure

This repository contains two main projects:

1. **content-footman-bot** - A personal assistant agent built with Mastra framework
2. **mcp-server** - An enhanced Model Context Protocol (MCP) server for Amazon.sg product scraping

## Common Development Commands

### MCP Server (mcp-server)

Build and run the server:
```bash
npm run build
npm start
```

Development mode with hot reload:
```bash
npm run dev
```

Database setup:
```bash
npm run db:setup
```

Testing:
```bash
npm test
npm run test:watch
npm run test:coverage
```

Linting:
```bash
npm run lint
npm run lint:fix
```

MCP-specific commands:
```bash
npm run mcp          # Run MCP server in development mode
npm run mcp:build    # Build and run MCP server
```

Prisma database commands:
```bash
npm run prisma:generate
npm run prisma:migrate
npm run prisma:studio
```

### Content Footman Bot (content-footman-bot)

Development:
```bash
npm run dev
```

Build:
```bash
npm run build
```

## Architecture Overview

### MCP Server

The MCP server implements the Model Context Protocol to provide Amazon product scraping capabilities:

1. **Core Components**:
   - Express.js web server with TypeScript
   - Prisma ORM for PostgreSQL database interactions
   - MCP server using @modelcontextprotocol/sdk
   - Amazon scraping services with proxy rotation

2. **Key Modules**:
   - `handlers/` - MCP tool and resource handlers
   - `services/` - Business logic (scraper, proxy management)
   - `lib/` - Core library instances (Prisma client)
   - `types/` - TypeScript interfaces and types
   - `utils/` - Helper functions (response formatting)

3. **MCP Tools**:
   - `search-amazon-products` - Search for products with filters
   - `get-product-details` - Extract comprehensive product information
   - `fetch-product-image` - Download product images

4. **MCP Resources**:
   - `search-history` - Access recent search queries
   - `product-database` - Access stored product information

5. **Database Schema**:
   - Product model with enhanced fields (arrays for features, specifications, highlights)
   - SearchQuery model for tracking search history
   - Proxy model for proxy rotation management
   - ScrapingLog model for logging scraping activities

### Content Footman Bot

The personal assistant agent uses the Mastra framework:

1. **Core Components**:
   - Mastra agents for AI interactions
   - Telegram integration for user interface
   - MCP connections to external services
   - Filesystem-based memory storage

2. **Key Modules**:
   - `agents/` - AI agent definitions
   - `integrations/` - Service integrations (Telegram)
   - `workflows/` - Complex multi-step processes
   - `tools/` - Custom tool implementations

3. **Features**:
   - Weather information retrieval
   - Daily briefing workflows
   - Telegram bot interface
   - Integration with various MCP servers (GitHub, Hacker News, etc.)