import TelegramBot from "node-telegram-bot-api";
import { personalAssistantAgent } from "../agents/personalAssistantAgent";
import { messageSplitterAgent } from "../agents/messageSplitterAgent";

export class TelegramIntegration {
  private bot: TelegramBot;
  private readonly MAX_MESSAGE_LENGTH = 4096; // Telegram's message length limit
  private readonly MAX_RESULT_LENGTH = 500; // Maximum length for tool results
  private readonly THINKING_THRESHOLD = ; // Stop updating message when content exceeds this length

  constructor(token: string) {
    // Create a bot instance
    this.bot = new TelegramBot(token, { polling: true });

    // Handle incoming messages
    this.bot.on("message", this.handleMessage.bind(this));
  }

  private escapeMarkdown(text: string): string {
    // Escape special Markdown characters
    return text.replace(/[_*[\]()~`>#+=|{}.!-]/g, "\\$&");
  }

  private truncateString(str: string, maxLength: number): string {
    if (str.length <= maxLength) return str;
    return str.substring(0, maxLength) + "... [truncated]";
  }

  private formatToolResult(result: any): string {
    try {
      const jsonString = JSON.stringify(result, null, 2);
      return this.escapeMarkdown(
        this.truncateString(jsonString, this.MAX_RESULT_LENGTH)
      );
    } catch (error) {
      return `[Complex data structure - ${typeof result}]`;
    }
  }

  private async splitMessageWithLLM(content: string): Promise<string[]> {
    try {
      // Use the personal assistant agent to split the message
      const splitPrompt = `Please split the following message into smaller parts that are each under ${this.MAX_MESSAGE_LENGTH} characters. Each part should be complete and make sense on its own. Preserve formatting and structure. Return the parts as a JSON array of strings.

Content to split:
${content}

Return only the JSON array, no other text.`;

      const response = await messageSplitterAgent.generate(splitPrompt);
      // Try to parse the response as JSON
      const parts = JSON.parse(response.text);
      if (Array.isArray(parts)) {
        return parts.filter(part => typeof part === 'string' && part.trim().length > 0);
      }
    } catch (error) {
      console.error("Error splitting message with LLM:", error);
    }

    // Fallback: simple character-based splitting
    return this.fallbackSplitMessage(content);
  }

  private fallbackSplitMessage(content: string): string[] {
    const parts: string[] = [];
    let currentPart = "";
    const lines = content.split('\n');

    for (const line of lines) {
      if (currentPart.length + line.length + 1 > this.MAX_MESSAGE_LENGTH - 100) {
        if (currentPart.trim()) {
          parts.push(currentPart.trim());
        }
        currentPart = line;
      } else {
        currentPart += (currentPart ? '\n' : '') + line;
      }
    }

    if (currentPart.trim()) {
      parts.push(currentPart.trim());
    }

    return parts.length > 0 ? parts : [content];
  }

  private async updateOrSplitMessage(
    chatId: number,
    messageId: number | undefined,
    text: string
  ): Promise<number> {
    // If text is within limits, try to update existing message
    if (text.length <= this.MAX_MESSAGE_LENGTH && messageId) {
      try {
        await this.bot.editMessageText(text, {
          chat_id: chatId,
          message_id: messageId,
          parse_mode: "MarkdownV2",
        });
        return messageId;
      } catch (error) {
        console.error("Error updating message:", error);
      }
    }

    // If text is too long or update failed, send as new message
    try {
      const newMessage = await this.bot.sendMessage(chatId, text, {
        parse_mode: "MarkdownV2",
      });
      return newMessage.message_id;
    } catch (error) {
      console.error("Error sending message:", error);
      // If the message is still too long, truncate it
      const truncated =
        text.substring(0, this.MAX_MESSAGE_LENGTH - 100) +
        "\n\n... [Message truncated due to length]";
      const fallbackMsg = await this.bot.sendMessage(chatId, truncated, {
        parse_mode: "MarkdownV2",
      });
      return fallbackMsg.message_id;
    }
  }

  private async handleMessage(msg: TelegramBot.Message) {
    const chatId = msg.chat.id;
    const text = msg.text;
    const username = msg.from?.username || "unknown";
    const firstName = msg.from?.first_name || "unknown";
    const userId = msg.from?.id.toString() || `anonymous-${chatId}`;

    if (!text) {
      await this.bot.sendMessage(
        chatId,
        "Sorry, I can only process text messages."
      );
      return;
    }

    try {
      // Send initial message
      const sentMessage = await this.bot.sendMessage(chatId, "Thinking...");
      let currentResponse = "";
      let lastUpdate = Date.now();
      let currentMessageId = sentMessage.message_id;
      let isThinkingOnly = false; // Flag to track if we're only showing "Thinking..."
      const UPDATE_INTERVAL = 500; // Update every 500ms to avoid rate limits

      // Stream response using the agent
      const stream = await personalAssistantAgent.stream(text, {
        threadId: `telegram-${chatId}`, // Use chat ID as thread ID
        resourceId: userId, // Use user ID as resource ID
        context: [
          {
            role: "system",
            content: `Current user: ${firstName} (${username})`,
          },
        ],
      });

      // Process the full stream
      for await (const chunk of stream.fullStream) {
        let shouldUpdate = false;
        let chunkText = "";

        switch (chunk.type) {
          case "text-delta":
            chunkText = this.escapeMarkdown(chunk.textDelta);
            shouldUpdate = true;
            break;

          case "tool-call":
            const formattedArgs = JSON.stringify(chunk.args, null, 2);
            chunkText = `\n🛠️ Using tool: ${this.escapeMarkdown(
              chunk.toolName
            )}\nArguments:\n\`\`\`\n${this.escapeMarkdown(
              formattedArgs
            )}\n\`\`\`\n`;
            console.log(`Tool call: ${chunk.toolName}`, chunk.args);
            shouldUpdate = true;
            break;

          case "tool-result":
            const formattedResult = this.formatToolResult(chunk.result);
            chunkText = `✨ Result:\n\`\`\`\n${formattedResult}\n\`\`\`\n`;
            console.log("Tool result:", chunk.result);
            shouldUpdate = true;
            break;

          case "error":
            chunkText = `\n❌ Error: ${this.escapeMarkdown(
              String(chunk.error)
            )}\n`;
            console.error("Error:", chunk.error);
            shouldUpdate = true;
            break;

          case "reasoning":
            chunkText = `\n💭 ${this.escapeMarkdown(chunk.textDelta)}\n`;
            console.log("Reasoning:", chunk.textDelta);
            shouldUpdate = true;
            break;
        }

        if (shouldUpdate) {
          currentResponse += chunkText;

          // Check if message is getting too long
          if (currentResponse.length > this.THINKING_THRESHOLD && !isThinkingOnly) {
            // Switch to thinking-only mode
            isThinkingOnly = true;
            try {
              await this.bot.editMessageText("Thinking...", {
                chat_id: chatId,
                message_id: currentMessageId,
              });
            } catch (error) {
              console.error("Error updating to thinking message:", error);
            }
          } else if (!isThinkingOnly) {
            // Continue updating normally if under threshold
            const now = Date.now();
            if (now - lastUpdate >= UPDATE_INTERVAL) {
              try {
                currentMessageId = await this.updateOrSplitMessage(
                  chatId,
                  currentMessageId,
                  currentResponse
                );
                lastUpdate = now;
              } catch (error) {
                console.error("Error updating/splitting message:", error);
              }
            }
          }
        }
      }

      // After streaming is complete, clear thinking message and send split messages
      if (isThinkingOnly) {
        // Clear the thinking message
        try {
          await this.bot.deleteMessage(chatId, currentMessageId);
        } catch (error) {
          console.error("Error deleting thinking message:", error);
        }

        // Split the content and send as separate messages
        const messageParts = await this.splitMessageWithLLM(currentResponse);
        for (const part of messageParts) {
          try {
            await this.bot.sendMessage(chatId, part, {
              parse_mode: "MarkdownV2",
            });
            // Small delay between messages to avoid rate limits
            await new Promise(resolve => setTimeout(resolve, 100));
          } catch (error) {
            console.error("Error sending split message:", error);
            // Fallback: send without markdown
            try {
              await this.bot.sendMessage(chatId, part);
            } catch (fallbackError) {
              console.error("Error sending fallback message:", fallbackError);
            }
          }
        }
      } else {
        // Final update for short messages
        await this.updateOrSplitMessage(
          chatId,
          currentMessageId,
          currentResponse
        );
      }
    } catch (error) {
      console.error("Error processing message:", error);
      await this.bot.sendMessage(
        chatId,
        "Sorry, I encountered an error processing your message. Please try again."
      );
    }
  }
}
