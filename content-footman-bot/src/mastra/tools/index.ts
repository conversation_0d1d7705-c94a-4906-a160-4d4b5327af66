import { createTool } from "@mastra/core/tools";
import { z } from "zod";
import { mastra } from "..";

interface GeocodingResponse {
  results: {
    latitude: number;
    longitude: number;
    name: string;
  }[];
}
interface WeatherResponse {
  current: {
    time: string;
    temperature_2m: number;
    apparent_temperature: number;
    relative_humidity_2m: number;
    wind_speed_10m: number;
    wind_gusts_10m: number;
    weather_code: number;
  };
}

export const weatherTool = createTool({
  id: "get-weather",
  description: "Get current weather for a location",
  inputSchema: z.object({
    location: z.string().describe("City name"),
  }),
  outputSchema: z.object({
    temperature: z.number(),
    feelsLike: z.number(),
    humidity: z.number(),
    windSpeed: z.number(),
    windGust: z.number(),
    conditions: z.string(),
    location: z.string(),
  }),
  execute: async ({ context }) => {
    return await getWeather(context.location);
  },
});

// Amazon Product Tools
export const searchAmazonProductsTool = createTool({
  id: "search-amazon-products",
  description: "Search for products on Amazon.sg with optional price filtering and return product information including ASINs, titles, prices, ratings, and reviews",
  inputSchema: z.object({
    topic: z.string().describe("Search query/topic for products"),
    limit: z.number().min(1).max(50).default(10).describe("Maximum number of products to return (1-50)"),
    lowPrice: z.number().min(0).optional().describe("Minimum price filter (optional)"),
    highPrice: z.number().min(0).optional().describe("Maximum price filter (optional)"),
  }),
  outputSchema: z.object({
    success: z.boolean(),
    searchTerm: z.string(),
    totalFound: z.number(),
    products: z.array(z.object({
      asin: z.string(),
      position: z.number(),
      title: z.string(),
      price: z.number().nullable(),
      rating: z.string(),
      review: z.string(),
    })),
    message: z.string().optional(),
  }),
  execute: async ({ context }) => {
    const { searchAmazonProducts } = await import("../services/amazon-scraper");
    return await searchAmazonProducts(context);
  },
});

export const getProductDetailsTool = createTool({
  id: "get-product-details",
  description: "Fetch detailed information about an Amazon product using its ASIN",
  inputSchema: z.object({
    asin: z.string().describe("Amazon Standard Identification Number (ASIN) of the product"),
  }),
  outputSchema: z.object({
    success: z.boolean(),
    asin: z.string(),
    product: z.object({
      title: z.string(),
      price: z.object({
        amount: z.number().nullable(),
        currency: z.string(),
        formatted: z.string(),
      }),
      rating: z.object({
        value: z.string(),
        formatted: z.string(),
      }),
      reviews: z.object({
        count: z.string(),
        formatted: z.string(),
      }),
      images: z.object({
        main: z.string(),
      }),
      features: z.array(z.string()),
      url: z.string(),
      content: z.string(),
      description: z.string(),
      specifications: z.array(z.string()),
      aboutThisItem: z.array(z.string()),
    }).optional(),
    error: z.string().optional(),
  }),
  execute: async ({ context }) => {
    const { getAmazonProductDetails } = await import("../services/amazon-scraper");
    return await getAmazonProductDetails(context.asin);
  },
});

export const fetchProductImageTool = createTool({
  id: "fetch-product-image",
  description: "Download and return a product image from Amazon",
  inputSchema: z.object({
    asin: z.string().describe("Amazon Standard Identification Number (ASIN) of the product"),
    imageType: z.enum(["main", "thumbnail"]).default("main").describe("Type of image to fetch"),
  }),
  outputSchema: z.object({
    success: z.boolean(),
    asin: z.string(),
    imageType: z.string(),
    message: z.string().optional(),
    error: z.string().optional(),
  }),
  execute: async ({ context }) => {
    const { fetchAmazonProductImage } = await import("../services/amazon-scraper");
    return await fetchAmazonProductImage(context.asin, context.imageType);
  },
});

export const searchAndGetProductDetailsTool = createTool({
  id: "search-and-get-product-details",
  description: "Search for Amazon products and automatically fetch detailed information for each product, returning a comprehensive structured list with full product details",
  inputSchema: z.object({
    topic: z.string().describe("Search query/topic for products"),
    limit: z.number().min(1).max(20).default(5).describe("Maximum number of products to return with details (1-20, default: 5). Note: Lower limits recommended due to detailed fetching"),
    lowPrice: z.number().min(0).optional().describe("Minimum price filter (optional)"),
    highPrice: z.number().min(0).optional().describe("Maximum price filter (optional)"),
  }),
  outputSchema: z.object({
    success: z.boolean(),
    searchTerm: z.string(),
    totalFound: z.number(),
    productsWithDetails: z.array(z.object({
      // Basic search info
      asin: z.string(),
      position: z.number(),
      searchTitle: z.string(),
      searchPrice: z.number().nullable(),
      searchRating: z.string(),
      searchReview: z.string(),
      // Detailed info
      detailsSuccess: z.boolean(),
      details: z.object({
        title: z.string(),
        price: z.object({
          amount: z.number().nullable(),
          currency: z.string(),
          formatted: z.string(),
        }),
        rating: z.object({
          value: z.string(),
          formatted: z.string(),
        }),
        reviews: z.object({
          count: z.string(),
          formatted: z.string(),
        }),
        images: z.object({
          main: z.string(),
        }),
        features: z.array(z.string()),
        url: z.string(),
        content: z.string(),
        description: z.string(),
        specifications: z.array(z.string()),
        aboutThisItem: z.array(z.string()),
      }).optional(),
      detailsError: z.string().optional(),
    })),
    message: z.string().optional(),
    errors: z.array(z.string()).optional(),
  }),
  execute: async ({ context }) => {
    const { searchAmazonProducts, getAmazonProductDetails } = await import("../services/amazon-scraper");

    try {
      // First, search for products
      const searchResult = await searchAmazonProducts(context);

      if (!searchResult.success) {
        return {
          success: false,
          searchTerm: context.topic,
          totalFound: 0,
          productsWithDetails: [],
          message: searchResult.message || "Search failed",
          errors: ["Failed to search for products"],
        };
      }

      // Then, fetch detailed information for each product
      const productsWithDetails = [];
      const errors = [];

      for (const product of searchResult.products) {
        try {
          const detailsResult = await getAmazonProductDetails(product.asin);

          productsWithDetails.push({
            // Basic search info
            asin: product.asin,
            position: product.position,
            searchTitle: product.title,
            searchPrice: product.price,
            searchRating: product.rating,
            searchReview: product.review,
            // Detailed info
            detailsSuccess: detailsResult.success,
            details: detailsResult.product,
            detailsError: detailsResult.error,
          });
        } catch (error: any) {
          errors.push(`Failed to get details for ASIN ${product.asin}: ${error.message}`);
          productsWithDetails.push({
            // Basic search info
            asin: product.asin,
            position: product.position,
            searchTitle: product.title,
            searchPrice: product.price,
            searchRating: product.rating,
            searchReview: product.review,
            // Detailed info
            detailsSuccess: false,
            detailsError: `Failed to fetch details: ${error.message}`,
          });
        }
      }

      const successfulDetails = productsWithDetails.filter(p => p.detailsSuccess).length;
      const message = `Found ${searchResult.totalFound} products for "${context.topic}". Successfully fetched detailed information for ${successfulDetails} out of ${searchResult.totalFound} products.`;

      return {
        success: true,
        searchTerm: context.topic,
        totalFound: searchResult.totalFound,
        productsWithDetails,
        message,
        errors: errors.length > 0 ? errors : undefined,
      };
    } catch (error: any) {
      return {
        success: false,
        searchTerm: context.topic,
        totalFound: 0,
        productsWithDetails: [],
        message: `Error in search and details operation: ${error.message}`,
        errors: [error.message],
      };
    }
  },
});

const getWeather = async (location: string) => {
  const geocodingUrl = `https://geocoding-api.open-meteo.com/v1/search?name=${encodeURIComponent(
    location
  )}&count=1`;
  const geocodingResponse = await fetch(geocodingUrl);
  const geocodingData = (await geocodingResponse.json()) as GeocodingResponse;

  if (!geocodingData.results?.[0]) {
    throw new Error(`Location '${location}' not found`);
  }

  const { latitude, longitude, name } = geocodingData.results[0];

  const weatherUrl = `https://api.open-meteo.com/v1/forecast?latitude=${latitude}&longitude=${longitude}&current=temperature_2m,apparent_temperature,relative_humidity_2m,wind_speed_10m,wind_gusts_10m,weather_code`;

  const response = await fetch(weatherUrl);
  const data = (await response.json()) as WeatherResponse;

  return {
    temperature: data.current.temperature_2m,
    feelsLike: data.current.apparent_temperature,
    humidity: data.current.relative_humidity_2m,
    windSpeed: data.current.wind_speed_10m,
    windGust: data.current.wind_gusts_10m,
    conditions: getWeatherCondition(data.current.weather_code),
    location: name,
  };
};

function getWeatherCondition(code: number): string {
  const conditions: Record<number, string> = {
    0: "Clear sky",
    1: "Mainly clear",
    2: "Partly cloudy",
    3: "Overcast",
    45: "Foggy",
    48: "Depositing rime fog",
    51: "Light drizzle",
    53: "Moderate drizzle",
    55: "Dense drizzle",
    56: "Light freezing drizzle",
    57: "Dense freezing drizzle",
    61: "Slight rain",
    63: "Moderate rain",
    65: "Heavy rain",
    66: "Light freezing rain",
    67: "Heavy freezing rain",
    71: "Slight snow fall",
    73: "Moderate snow fall",
    75: "Heavy snow fall",
    77: "Snow grains",
    80: "Slight rain showers",
    81: "Moderate rain showers",
    82: "Violent rain showers",
    85: "Slight snow showers",
    86: "Heavy snow showers",
    95: "Thunderstorm",
    96: "Thunderstorm with slight hail",
    99: "Thunderstorm with heavy hail",
  };
  return conditions[code] || "Unknown";
}

// export const dailyWorkflowTool = createTool({
//   id: "daily-workflow-tool",
//   description:
//     "Runs the daily workflow task which returns a summary of news and github activity",

//   outputSchema: z.object({
//     message: z.string(),
//   }),
//   execute: async ({ context }) => {
//     const { runId, start } = mastra.getWorkflow("dailyWorkflow").createRun();
//     const result = await start();
//     return {
//       message: result?.result?.message || "",
//     };
//   },
// });
