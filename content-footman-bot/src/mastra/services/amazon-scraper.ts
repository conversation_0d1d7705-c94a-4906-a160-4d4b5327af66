/**
 * Amazon Scraping Service for Mastra
 * 
 * This service handles all Amazon scraping operations including product search,
 * product details extraction, and image fetching.
 * Adapted from MCP server for Mastra framework.
 */

import axios from 'axios';
import * as cheerio from 'cheerio';

// Amazon base URL
const AMAZON_BASE_URL = "https://www.amazon.sg";

export interface ProductSearchOptions {
  topic: string;
  limit?: number;
  lowPrice?: number;
  highPrice?: number;
}

export interface ProductSearchResult {
  success: boolean;
  searchTerm: string;
  totalFound: number;
  products: Array<{
    asin: string;
    position: number;
    title: string;
    price: number | null;
    rating: string;
    review: string;
  }>;
  message?: string;
}

export interface ProductDetails {
  title: string;
  price: number | null;
  currency: string;
  rating: string;
  reviewCount: string;
  mainImage: string;
  features: string[];
  url: string;
  content: string;
  description: string;
  specifications: string[];
  aboutThisItem: string[];
}

export interface ProductDetailsResult {
  success: boolean;
  asin: string;
  product?: {
    title: string;
    price: {
      amount: number | null;
      currency: string;
      formatted: string;
    };
    rating: {
      value: string;
      formatted: string;
    };
    reviews: {
      count: string;
      formatted: string;
    };
    images: {
      main: string;
    };
    features: string[];
    url: string;
    content: string;
    description: string;
    specifications: string[];
    aboutThisItem: string[];
  };
  error?: string;
}

export interface ImageFetchResult {
  success: boolean;
  asin: string;
  imageType: string;
  message?: string;
  error?: string;
}

// User agents for rotation
const USER_AGENTS = [
  'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
  'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
];

function getRandomUserAgent(): string {
  return USER_AGENTS[Math.floor(Math.random() * USER_AGENTS.length)];
}

function createAxiosConfig(): any {
  return {
    headers: {
      'User-Agent': getRandomUserAgent(),
      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
      'Accept-Language': 'en-US,en;q=0.5',
      'Accept-Encoding': 'gzip, deflate',
      'Connection': 'keep-alive',
      'Upgrade-Insecure-Requests': '1',
    },
    timeout: 30000,
  };
}

/**
 * Search for products on Amazon
 */
export async function searchAmazonProducts(options: ProductSearchOptions): Promise<ProductSearchResult> {
  const { topic, limit = 10, lowPrice, highPrice } = options;

  try {
    // Build search URL
    let searchUrl = `${AMAZON_BASE_URL}/s?k=${encodeURIComponent(topic)}`;

    if (lowPrice !== undefined) {
      searchUrl += `&low-price=${lowPrice}`;
    }
    if (highPrice !== undefined) {
      searchUrl += `&high-price=${highPrice}`;
    }

    console.log(`Searching Amazon for: ${topic}`);

    const response = await axios.get(searchUrl, createAxiosConfig());
    const $ = cheerio.load(response.data);

    // Extract product information from the page
    const products: Array<{
      asin: string;
      position: number;
      title: string;
      price: number | null;
      rating: string;
      review: string;
    }> = [];

    // Look for elements with data-asin attribute (Amazon's product containers)
    $("[role=listitem][data-asin]").each((_index, element) => {
      const asin = $(element).attr("data-asin");

      // Only process valid ASINs and respect the limit
      if (asin && asin.trim() !== "" && products.length < limit) {
        const $product = $(element);

        // Extract title
        const title = $product.find('h2 a span, .a-size-mini span, .a-size-base-plus').first().text().trim() || 'Title not found';

        // Extract price
        const priceText = $product.find('.a-price-whole, .a-price .a-offscreen').first().text().trim();
        const price = priceText ? parseFloat(priceText.replace(/[^\d.]/g, '')) : null;

        // Extract rating
        const ratingText = $product.find('.a-icon-alt').first().text();
        const rating = ratingText.match(/[\d.]+/)?.[0] || '';

        // Extract review count
        const reviewText = $product.find('.a-size-base').filter((_, el) => $(el).text().includes('(')).text();
        const review = reviewText.match(/\(([^)]+)\)/)?.[1] || '';

        products.push({
          asin,
          position: products.length + 1,
          title,
          price,
          rating,
          review
        });
      }
    });

    const totalFound = products.length;
    const message = `Found ${totalFound} products for "${topic}". Use the "get-product-details" tool with any of these ASINs to get detailed product information.`;

    return {
      success: true,
      searchTerm: topic,
      totalFound,
      products,
      message,
    };
  } catch (error: any) {
    console.error('Error searching for products:', error);
    return {
      success: false,
      searchTerm: topic,
      totalFound: 0,
      products: [],
      message: `Error searching for products: ${error.message}`,
    };
  }
}

/**
 * Get detailed information about a specific product
 */
export async function getAmazonProductDetails(asin: string): Promise<ProductDetailsResult> {
  try {
    const productUrl = `${AMAZON_BASE_URL}/dp/${asin}`;
    console.log(`Fetching product details for ASIN: ${asin}`);

    const response = await axios.get(productUrl, createAxiosConfig());
    const $ = cheerio.load(response.data);

    // Extract product details
    const title = $('#productTitle').text().trim();
    const priceText = $('.a-price-whole').first().text().trim();
    const price = priceText ? parseFloat(priceText.replace(/[^0-9.]/g, '')) : null;
    const currency = $('.a-price-symbol').first().text().trim() || 'SGD';

    const rating = $('.a-icon-alt').first().text().trim();
    const reviewCount = $('.a-size-base').first().text().trim();

    const mainImage = $('#landingImage').attr('src') || '';

    // Extract features
    const features: string[] = [];
    $('#feature-bullets ul li').each((_, element) => {
      const feature = $(element).text().trim();
      if (feature && !feature.includes('Make sure this fits')) {
        features.push(feature);
      }
    });

    // Extract specifications
    const specifications: string[] = [];
    $('#productDetails_techSpec_section_1 tr').each((_, element) => {
      const label = $(element).find('td:first-child').text().trim();
      const value = $(element).find('td:last-child').text().trim();
      if (label && value) {
        specifications.push(`${label}: ${value}`);
      }
    });

    // Extract about this item
    const aboutThisItem: string[] = [];
    $('#feature-bullets ul li').each((_, element) => {
      const item = $(element).text().trim();
      if (item && !item.includes('Make sure this fits')) {
        aboutThisItem.push(item);
      }
    });

    const description = $('#productDescription p').text().trim();
    const content = `${title}\n\nPrice: ${currency} ${price || 'Not available'}\nRating: ${rating || 'Not available'}\nReviews: ${reviewCount || 'Not available'}\n\nFeatures:\n${features.join('\n')}\n\nSpecifications:\n${specifications.join('\n')}\n\nDescription:\n${description}`;

    const productDetails: ProductDetailsResult = {
      success: true,
      asin,
      product: {
        title,
        price: {
          amount: price,
          currency,
          formatted: price ? `${currency} ${price}` : "Not available",
        },
        rating: {
          value: rating,
          formatted: rating ? `${rating}/5` : "Not available",
        },
        reviews: {
          count: reviewCount,
          formatted: reviewCount || "Not available",
        },
        images: {
          main: mainImage,
        },
        features,
        url: productUrl,
        content,
        description,
        specifications,
        aboutThisItem,
      },
    };

    return productDetails;
  } catch (error: any) {
    console.error(`Error fetching product details for ASIN ${asin}:`, error);
    return {
      success: false,
      asin,
      error: `Error fetching product details for ASIN ${asin}: ${error.message}`,
    };
  }
}

/**
 * Fetch product image
 */
export async function fetchAmazonProductImage(asin: string, imageType: "main" | "thumbnail" = "main"): Promise<ImageFetchResult> {
  try {
    // For now, we'll return a success message indicating the image URL
    // In a full implementation, you might want to actually download and return the image data
    const productDetails = await getAmazonProductDetails(asin);

    if (!productDetails.success || !productDetails.product?.images.main) {
      return {
        success: false,
        asin,
        imageType,
        error: `No image URL found for ASIN ${asin}. Try running "get-product-details" first to fetch product information.`,
      };
    }

    return {
      success: true,
      asin,
      imageType,
      message: `Successfully fetched ${imageType} image for ASIN ${asin}. Image URL: ${productDetails.product.images.main}`,
    };
  } catch (error: any) {
    console.error(`Error fetching image for ASIN ${asin}:`, error);
    return {
      success: false,
      asin,
      imageType,
      error: `Error fetching image for ASIN ${asin}: ${error.message}`,
    };
  }
}

