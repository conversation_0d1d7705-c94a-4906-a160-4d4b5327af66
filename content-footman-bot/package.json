{"name": "content-footman-bot", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "<PERSON>ra dev", "build": "mastra build"}, "keywords": [], "author": "", "license": "ISC", "type": "module", "dependencies": {"@ai-sdk/google": "^1.2.22", "@ai-sdk/openai": "^1.3.22", "@mastra/core": "^0.10.5", "@mastra/libsql": "^0.10.2", "@mastra/mcp": "^0.10.3", "@mastra/memory": "^0.10.3", "axios": "^1.11.0", "cheerio": "^1.1.0", "mastra": "^0.10.15", "node-telegram-bot-api": "^0.66.0", "zod": "^3.25.67"}, "devDependencies": {"@types/cheerio": "^0.22.35", "@types/node": "^22.15.32", "@types/node-telegram-bot-api": "^0.64.9", "tsx": "^4.20.3", "typescript": "^5.8.3"}}